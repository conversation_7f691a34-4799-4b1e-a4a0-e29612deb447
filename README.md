# Professional reCAPTCHA Solver

A professional-grade reCAPTCHA solver built with modern software engineering practices. This solution uses advanced computer vision, machine learning, and browser automation to solve image-based reCAPTCHA challenges with high accuracy and reliability.

## 🚀 Features

### Core Capabilities
- **Multi-browser support**: Chrome, Firefox, and Edge with automatic binary detection
- **Advanced object detection**: YOLO-based detection with confidence scoring and IoU analysis
- **Dynamic challenge handling**: Supports static, dynamic, and multi-step reCAPTCHA challenges
- **Intelligent image processing**: Preprocessing, enhancement, and validation pipeline
- **Professional architecture**: Modular, extensible, and maintainable codebase

### Enterprise Features
- **Robust configuration management**: YAML/JSON configs with validation and environment variables
- **Comprehensive error handling**: Custom exceptions with retry mechanisms and circuit breakers
- **Performance monitoring**: Built-in analytics, timing metrics, and success rate tracking
- **Flexible deployment**: CLI interface, Python API, and future REST API support
- **Production ready**: Logging, caching, resource management, and cleanup

## 📋 Requirements

- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, or Linux
- **Browser**: Chrome, Firefox, or Edge (automatically detected)
- **Memory**: Minimum 4GB RAM recommended
- **Storage**: 500MB for models and dependencies

## 🛠 Installation

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd Recaptcha-Image

# Install dependencies
pip install -r requirements.txt

# Test the installation
python cli.py --test-config
```

### Development Installation
```bash
# Install with development dependencies
pip install -r requirements.txt

# Install pre-commit hooks (optional)
pre-commit install
```

## Usage

### Basic Usage

Run the solver with default settings:
```bash
python run.py
```

### Advanced Usage

```bash
# Use a specific browser
python run.py --browser firefox

# Use a proxy
python run.py --proxy 127.0.0.1:8080

# Specify custom Chrome binary path
python run.py --chrome-binary-path "C:\Path\To\Chrome\chrome.exe"

# Run on a specific URL
python run.py --url "https://example.com/recaptcha-page"

# Disable verbose output
python run.py --no-verbose
```

### Command Line Options

- `--url`: Target URL with reCAPTCHA (default: Google's demo page)
- `--browser`: Choose browser (chrome, firefox, edge)
- `--proxy`: Proxy server in format host:port
- `--no-verbose`: Disable verbose logging
- `--chrome-binary-path`: Custom Chrome binary path (auto-detected if not provided)

## Project Structure

```
Recaptcha-Image/
├── recaptcha_solver_enhanced.py  # Main solver implementation
├── run.py                        # Command-line interface
├── requirements.txt              # Python dependencies
├── models/
│   └── models.onnx              # YOLO detection model
└── README.md                    # This file
```

## How It Works

1. **Browser Automation**: Uses Selenium to control web browsers
2. **reCAPTCHA Detection**: Automatically detects and interacts with reCAPTCHA elements
3. **Image Processing**: Downloads and preprocesses challenge images
4. **Object Detection**: Uses YOLO model to identify target objects in images
5. **Grid Analysis**: Calculates which grid cells contain target objects using IoU
6. **Dynamic Handling**: Manages dynamic challenges with new images
7. **Verification**: Submits answers and verifies success

## Supported reCAPTCHA Types

- **3x3 Grid Selection**: Standard 9-cell image grids
- **4x4 Grid Selection**: 16-cell image grids
- **Dynamic Challenges**: Challenges that load new images after selection
- **Square Detection**: Various object types including vehicles, traffic signs, etc.

## Object Detection Classes

The solver can detect various objects including:
- Vehicles: cars, buses, trucks, motorcycles, bicycles
- Traffic elements: traffic lights, stop signs, crosswalks
- Infrastructure: bridges, stairs, chimneys
- Nature: trees, mountains
- Animals: cats, dogs, horses, etc.

## Troubleshooting

### Common Issues

1. **Chrome not found**: The solver will auto-detect Chrome. If it fails, specify the path manually:
   ```bash
   python run.py --chrome-binary-path "C:\Path\To\Chrome\chrome.exe"
   ```

2. **SSL Certificate errors**: The solver automatically handles SSL issues, but ensure your network allows HTTPS traffic.

3. **Model not found**: Ensure the `models/models.onnx` file exists in the project directory.

4. **Memory issues**: If you encounter memory problems, close unnecessary applications to free up memory.

### Performance Tips

- Ensure stable internet connection
- Close unnecessary applications to free up memory
- Use the latest browser versions

## Limitations

- Designed primarily for educational and research purposes
- Success rate depends on image quality and complexity
- May not work with all reCAPTCHA variations
- Requires periodic model updates for new challenge types

## Legal Notice

This tool is intended for educational and research purposes only. Users are responsible for ensuring compliance with applicable laws and terms of service when using this software.

## Contributing

Contributions are welcome! Please ensure your code follows the existing style and includes appropriate error handling.

## License

This project is provided as-is for educational purposes. Please respect the terms of service of websites you interact with.
