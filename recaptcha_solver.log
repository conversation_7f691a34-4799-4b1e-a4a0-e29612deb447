2025-07-22 19:08:03,858 - INFO - 
[36m============================================================[0m
2025-07-22 19:08:03,859 - INFO - [36m                  reCAPTCHA Solver Example                  [0m
2025-07-22 19:08:03,859 - INFO - [36m============================================================[0m
2025-07-22 19:08:03,860 - INFO - 
[33mAttempting to solve reCAPTCHA on:[0m https://www.google.com/recaptcha/api2/demo

2025-07-22 19:08:03,860 - INFO - [34mNote:[0m SSL certificate verification is disabled.

2025-07-22 19:08:03,865 - INFO - Y<PERSON><PERSON> model loaded successfully from ./models/models.onnx
2025-07-22 19:08:03,868 - WARNING - Chrome binary not found in common locations
2025-07-22 19:08:03,869 - INFO - Using default request storage
2025-07-22 19:08:03,899 - INFO - Created proxy listening on 127.0.0.1:17963
2025-07-22 19:08:06,900 - INFO - Capturing request: http://clients2.google.com/time/1/current?cup2key=9:hyyKnr3XXxiNl6gTA7SyCOvAYzbohtBWEbXN0LdkBg8&cup2hreq=e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
2025-07-22 19:08:07,025 - INFO - Capturing response: http://clients2.google.com/time/1/current?cup2key=9:hyyKnr3XXxiNl6gTA7SyCOvAYzbohtBWEbXN0LdkBg8&cup2hreq=e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855 200 OK
2025-07-22 19:08:07,030 - INFO - Navigating to https://www.google.com/recaptcha/api2/demo
2025-07-22 19:08:07,339 - INFO - Capturing request: https://www.google.com/recaptcha/api2/demo
2025-07-22 19:08:07,482 - INFO - Capturing response: https://www.google.com/recaptcha/api2/demo 200 
2025-07-22 19:08:07,649 - INFO - Capturing request: https://www.google.com/recaptcha/api.js
2025-07-22 19:08:07,692 - INFO - Capturing response: https://www.google.com/recaptcha/api.js 200 
2025-07-22 19:08:08,699 - INFO - Capturing request: https://www.google.com/recaptcha/api2/anchor?ar=1&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&co=aHR0cHM6Ly93d3cuZ29vZ2xlLmNvbTo0NDM.&hl=en&v=3jpV4E_UA9gZWYy11LtggjoU&size=normal&sa=action&anchor-ms=20000&execute-ms=15000&cb=s9m4qt9b2hkm
2025-07-22 19:08:08,885 - INFO - Capturing response: https://www.google.com/recaptcha/api2/anchor?ar=1&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&co=aHR0cHM6Ly93d3cuZ29vZ2xlLmNvbTo0NDM.&hl=en&v=3jpV4E_UA9gZWYy11LtggjoU&size=normal&sa=action&anchor-ms=20000&execute-ms=15000&cb=s9m4qt9b2hkm 200 
2025-07-22 19:08:09,689 - INFO - Capturing request: https://www.google.com/recaptcha/api2/webworker.js?hl=en&v=3jpV4E_UA9gZWYy11LtggjoU
2025-07-22 19:08:09,732 - INFO - Capturing response: https://www.google.com/recaptcha/api2/webworker.js?hl=en&v=3jpV4E_UA9gZWYy11LtggjoU 200 
2025-07-22 19:08:10,875 - INFO - Capturing request: https://www.google.com/recaptcha/api2/bframe?hl=en&v=3jpV4E_UA9gZWYy11LtggjoU&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:11,031 - INFO - Capturing response: https://www.google.com/recaptcha/api2/bframe?hl=en&v=3jpV4E_UA9gZWYy11LtggjoU&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:11,280 - INFO - Challenge attempt 1/10
2025-07-22 19:08:11,292 - INFO - Capturing request: https://www.google.com/recaptcha/api2/reload?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:11,572 - INFO - Capturing response: https://www.google.com/recaptcha/api2/reload?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:11,822 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA4dDlQU3aY5a4SOlOv0DFd2Pc8AyVm8h91FHox-u-W_pUStVJY-ioEGUHLgVymnXZTzdAYx2DWqVM46dcI9glM-8cvcDGukazKIXYFOqAgdhEE5OQEQPAghHhLgX1y70DJlbTubURzc6RkjdeG3gYxosamQeroNhvwY6b0famn5nw9kkKmEqI31YaNXYoSNHJxQkag6VVEGGwaK7kKjiP8ZNW_sJw&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:11,912 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA4dDlQU3aY5a4SOlOv0DFd2Pc8AyVm8h91FHox-u-W_pUStVJY-ioEGUHLgVymnXZTzdAYx2DWqVM46dcI9glM-8cvcDGukazKIXYFOqAgdhEE5OQEQPAghHhLgX1y70DJlbTubURzc6RkjdeG3gYxosamQeroNhvwY6b0famn5nw9kkKmEqI31YaNXYoSNHJxQkag6VVEGGwaK7kKjiP8ZNW_sJw&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:12,460 - INFO - Downloaded image 0.png successfully (attempt 1)
2025-07-22 19:08:12,465 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:08:12,466 - INFO - Running IoU-based solver for target class: 12
2025-07-22 19:08:12,880 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:08:12,881 - INFO - Image dimensions: 300x300
2025-07-22 19:08:16,472 - INFO - Found 3 objects in the image
2025-07-22 19:08:16,473 - INFO - Looking for target class: 12
2025-07-22 19:08:16,474 - INFO - Found 0 valid instances of target class 12
2025-07-22 19:08:16,475 - INFO - Final answers (IoU-based): []
2025-07-22 19:08:16,475 - WARNING - No answers found, reloading challenge
2025-07-22 19:08:16,723 - INFO - Capturing request: https://www.google.com/recaptcha/api2/reload?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:16,962 - INFO - Capturing response: https://www.google.com/recaptcha/api2/reload?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:17,130 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA7rVp5ZZHH2OSdE33hk5TTMZwEVXoe3iPf9CFGEzI5vX03HN3Qu6Z6RsAKKuwK9LgMuT6JuP2jXOMxfT1YNNNJAZCIEGwbaj6i49j4QRjhY85mrIYp97jFY04J5rfgCYYx5sHW8BAsuVP3RSbmQwaqH7mVHc8sbJSd6rOjj8g5cHqJh4NAzt2-EdqRZYG4ow1HKjUjZA30x-cmfP6alNAqndi_qbg&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:17,176 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA7rVp5ZZHH2OSdE33hk5TTMZwEVXoe3iPf9CFGEzI5vX03HN3Qu6Z6RsAKKuwK9LgMuT6JuP2jXOMxfT1YNNNJAZCIEGwbaj6i49j4QRjhY85mrIYp97jFY04J5rfgCYYx5sHW8BAsuVP3RSbmQwaqH7mVHc8sbJSd6rOjj8g5cHqJh4NAzt2-EdqRZYG4ow1HKjUjZA30x-cmfP6alNAqndi_qbg&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:17,275 - INFO - Challenge attempt 2/10
2025-07-22 19:08:17,413 - INFO - Cleaned up 1 image files
2025-07-22 19:08:17,861 - INFO - Downloaded image 0.png successfully (attempt 1)
2025-07-22 19:08:17,862 - INFO - Square captcha found....
2025-07-22 19:08:17,862 - INFO - Running IoU-based square solver for target class: 3
2025-07-22 19:08:17,881 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:08:17,881 - INFO - Image dimensions: 450x450
2025-07-22 19:08:18,082 - INFO - Found 0 objects in the image
2025-07-22 19:08:18,082 - INFO - Looking for target class: 3
2025-07-22 19:08:18,083 - INFO - Found 0 valid instances of target class 3
2025-07-22 19:08:18,083 - INFO - Final answers (IoU-based, square): []
2025-07-22 19:08:18,084 - WARNING - No answers found, reloading challenge
2025-07-22 19:08:18,326 - INFO - Capturing request: https://www.google.com/recaptcha/api2/reload?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:18,595 - INFO - Capturing response: https://www.google.com/recaptcha/api2/reload?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:18,796 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA5aodCq4CLPhNkzBq-yawjstIDkld6tCXMoFkwWXENCK5merq2sKWrwpdZLIfM0kYa2bXnXGxF-z-844wVMKd3FuIJKy2cjkTnHdgokQjiO2y_j4nmNietU6HAZ1xL36gOPNd6fvXQ0TsKU2en6n6lpAp7DZMyRcLvFQ12oKK2HiqO1KxXcS71WxmY60cwQ3FUK194-7gryxDssSZ1eZ0bkbPxi3A&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:18,898 - INFO - Challenge attempt 3/10
2025-07-22 19:08:18,951 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA5aodCq4CLPhNkzBq-yawjstIDkld6tCXMoFkwWXENCK5merq2sKWrwpdZLIfM0kYa2bXnXGxF-z-844wVMKd3FuIJKy2cjkTnHdgokQjiO2y_j4nmNietU6HAZ1xL36gOPNd6fvXQ0TsKU2en6n6lpAp7DZMyRcLvFQ12oKK2HiqO1KxXcS71WxmY60cwQ3FUK194-7gryxDssSZ1eZ0bkbPxi3A&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:18,979 - INFO - Cleaned up 1 image files
2025-07-22 19:08:19,266 - INFO - Downloaded image 0.png successfully (attempt 1)
2025-07-22 19:08:19,268 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:08:19,268 - INFO - Running IoU-based solver for target class: 1
2025-07-22 19:08:19,274 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:08:19,275 - INFO - Image dimensions: 300x300
2025-07-22 19:08:19,461 - INFO - Found 4 objects in the image
2025-07-22 19:08:19,462 - INFO - Looking for target class: 1
2025-07-22 19:08:19,463 - INFO - Found target 1 with confidence 0.32
2025-07-22 19:08:19,463 - INFO - Found target 1 with confidence 0.31
2025-07-22 19:08:19,463 - INFO - Found 2 valid instances of target class 1
2025-07-22 19:08:19,464 - INFO - Cell 9 selected with IoU: 0.16
2025-07-22 19:08:19,465 - INFO - Final answers (IoU-based): [9]
2025-07-22 19:08:19,465 - INFO - Clicking 1 cells for dynamic captcha: [9]
2025-07-22 19:08:20,450 - INFO - Successfully clicked on cell 9
2025-07-22 19:08:20,464 - INFO - Capturing request: https://www.google.com/recaptcha/api2/replaceimage?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:20,513 - INFO - Capturing response: https://www.google.com/recaptcha/api2/replaceimage?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:20,630 - INFO - Dynamic captcha iteration 1/5
2025-07-22 19:08:20,633 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA48ckd7fbMkCySAgcvxj8kIfkSyXXkr0kiGjJ4xHfAUpa3Nb9-UGKwFEquQU-9zO3Ux6BugEj8ixwPFKHG07BNtwyAIGURFwTrWmXkYohEHmO1oCjsJZl_GkCcVQ2_DJL5fnAwTOXHFDwnmNjBGNxNTC9bzDBgWGU2DePtx-nuMq_DsW6bsLR-zTDueAFvWFakhTRsUtDhZOYI8OWeGn_2-uZhVYA&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&id=a9887827d930bbfa
2025-07-22 19:08:20,681 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA48ckd7fbMkCySAgcvxj8kIfkSyXXkr0kiGjJ4xHfAUpa3Nb9-UGKwFEquQU-9zO3Ux6BugEj8ixwPFKHG07BNtwyAIGURFwTrWmXkYohEHmO1oCjsJZl_GkCcVQ2_DJL5fnAwTOXHFDwnmNjBGNxNTC9bzDBgWGU2DePtx-nuMq_DsW6bsLR-zTDueAFvWFakhTRsUtDhZOYI8OWeGn_2-uZhVYA&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&id=a9887827d930bbfa 200 
2025-07-22 19:08:25,403 - INFO - Downloaded image 9.png successfully (attempt 1)
2025-07-22 19:08:25,492 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:08:25,495 - INFO - Running IoU-based solver for target class: 1
2025-07-22 19:08:25,511 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:08:25,512 - INFO - Image dimensions: 300x300
2025-07-22 19:08:25,772 - INFO - Found 1 objects in the image
2025-07-22 19:08:25,772 - INFO - Looking for target class: 1
2025-07-22 19:08:25,773 - INFO - Found 0 valid instances of target class 1
2025-07-22 19:08:25,773 - INFO - Final answers (IoU-based): []
2025-07-22 19:08:25,776 - INFO - No more answers found, dynamic captcha complete
2025-07-22 19:08:39,510 - WARNING - Verification failed: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff6a0f4e925+77845]
	GetHandleVerifier [0x0x7ff6a0f4e980+77936]
	(No symbol) [0x0x7ff6a0d09cda]
	(No symbol) [0x0x7ff6a0d606aa]
	(No symbol) [0x0x7ff6a0d6095c]
	(No symbol) [0x0x7ff6a0db3d07]
	(No symbol) [0x0x7ff6a0d8890f]
	(No symbol) [0x0x7ff6a0db0b07]
	(No symbol) [0x0x7ff6a0d886a3]
	(No symbol) [0x0x7ff6a0d51791]
	(No symbol) [0x0x7ff6a0d52523]
	GetHandleVerifier [0x0x7ff6a122683d+3059501]
	GetHandleVerifier [0x0x7ff6a1220bfd+3035885]
	GetHandleVerifier [0x0x7ff6a12403f0+3164896]
	GetHandleVerifier [0x0x7ff6a0f68c2e+185118]
	GetHandleVerifier [0x0x7ff6a0f7053f+216111]
	GetHandleVerifier [0x0x7ff6a0f572d4+113092]
	GetHandleVerifier [0x0x7ff6a0f57489+113529]
	GetHandleVerifier [0x0x7ff6a0f3e288+10616]
	BaseThreadInitThunk [0x0x7ffedafe1fd7+23]
	RtlUserThreadStart [0x0x7ffedccdd7d0+32]

2025-07-22 19:08:39,559 - INFO - Challenge attempt 4/10
2025-07-22 19:08:39,633 - INFO - Cleaned up 2 image files
2025-07-22 19:08:39,937 - INFO - Downloaded image 0.png successfully (attempt 1)
2025-07-22 19:08:39,938 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:08:39,939 - INFO - Running IoU-based solver for target class: 1
2025-07-22 19:08:39,945 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:08:39,946 - INFO - Image dimensions: 300x300
2025-07-22 19:08:40,121 - INFO - Found 4 objects in the image
2025-07-22 19:08:40,122 - INFO - Looking for target class: 1
2025-07-22 19:08:40,122 - INFO - Found target 1 with confidence 0.32
2025-07-22 19:08:40,122 - INFO - Found target 1 with confidence 0.31
2025-07-22 19:08:40,123 - INFO - Found 2 valid instances of target class 1
2025-07-22 19:08:40,124 - INFO - Cell 9 selected with IoU: 0.16
2025-07-22 19:08:40,124 - INFO - Final answers (IoU-based): [9]
2025-07-22 19:08:40,124 - INFO - Clicking 1 cells for dynamic captcha: [9]
2025-07-22 19:08:40,656 - INFO - Successfully clicked on cell 9
2025-07-22 19:08:40,673 - INFO - Capturing request: https://www.google.com/recaptcha/api2/replaceimage?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:08:40,832 - INFO - Capturing response: https://www.google.com/recaptcha/api2/replaceimage?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:08:40,872 - INFO - Dynamic captcha iteration 1/5
2025-07-22 19:08:40,969 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA4zNztHCj8km9S6o-cl-snb7sm1Wkz-hju_wrMmNO9pKyokE1GU_J2kP-bLRhkLyRae-ky-t6Y8WG7y971CgBsJwGroRGFKRDzGAphOCxd5enIYkjARZ54QXvwfAWKVQ7jgTbn9i59KDBsHG0iLPuY2GV_LB_MXLsMMcNNUWcQVRnjHB4AuwdjQwtirMPD4GskhAp8KWGcyGU1_BISUDEWVadwTlA&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&id=ff7e03e64585128c
2025-07-22 19:08:41,007 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA4zNztHCj8km9S6o-cl-snb7sm1Wkz-hju_wrMmNO9pKyokE1GU_J2kP-bLRhkLyRae-ky-t6Y8WG7y971CgBsJwGroRGFKRDzGAphOCxd5enIYkjARZ54QXvwfAWKVQ7jgTbn9i59KDBsHG0iLPuY2GV_LB_MXLsMMcNNUWcQVRnjHB4AuwdjQwtirMPD4GskhAp8KWGcyGU1_BISUDEWVadwTlA&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&id=ff7e03e64585128c 200 
2025-07-22 19:08:45,875 - INFO - Downloaded image 9.png successfully (attempt 1)
2025-07-22 19:08:45,890 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:08:45,890 - INFO - Running IoU-based solver for target class: 1
2025-07-22 19:08:45,904 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:08:45,904 - INFO - Image dimensions: 300x300
2025-07-22 19:08:46,106 - INFO - Found 2 objects in the image
2025-07-22 19:08:46,106 - INFO - Looking for target class: 1
2025-07-22 19:08:46,106 - INFO - Found 0 valid instances of target class 1
2025-07-22 19:08:46,107 - INFO - Final answers (IoU-based): []
2025-07-22 19:08:46,107 - INFO - No more answers found, dynamic captcha complete
2025-07-22 19:08:59,327 - WARNING - Verification failed: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff6a0f4e925+77845]
	GetHandleVerifier [0x0x7ff6a0f4e980+77936]
	(No symbol) [0x0x7ff6a0d09cda]
	(No symbol) [0x0x7ff6a0d606aa]
	(No symbol) [0x0x7ff6a0d6095c]
	(No symbol) [0x0x7ff6a0db3d07]
	(No symbol) [0x0x7ff6a0d8890f]
	(No symbol) [0x0x7ff6a0db0b07]
	(No symbol) [0x0x7ff6a0d886a3]
	(No symbol) [0x0x7ff6a0d51791]
	(No symbol) [0x0x7ff6a0d52523]
	GetHandleVerifier [0x0x7ff6a122683d+3059501]
	GetHandleVerifier [0x0x7ff6a1220bfd+3035885]
	GetHandleVerifier [0x0x7ff6a12403f0+3164896]
	GetHandleVerifier [0x0x7ff6a0f68c2e+185118]
	GetHandleVerifier [0x0x7ff6a0f7053f+216111]
	GetHandleVerifier [0x0x7ff6a0f572d4+113092]
	GetHandleVerifier [0x0x7ff6a0f57489+113529]
	GetHandleVerifier [0x0x7ff6a0f3e288+10616]
	BaseThreadInitThunk [0x0x7ffedafe1fd7+23]
	RtlUserThreadStart [0x0x7ffedccdd7d0+32]

2025-07-22 19:08:59,377 - INFO - Challenge attempt 5/10
2025-07-22 19:08:59,445 - INFO - Cleaned up 2 image files
2025-07-22 19:08:59,763 - INFO - Downloaded image 0.png successfully (attempt 1)
2025-07-22 19:08:59,764 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:08:59,765 - INFO - Running IoU-based solver for target class: 1
2025-07-22 19:08:59,770 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:08:59,771 - INFO - Image dimensions: 300x300
2025-07-22 19:08:59,959 - INFO - Found 4 objects in the image
2025-07-22 19:08:59,960 - INFO - Looking for target class: 1
2025-07-22 19:08:59,961 - INFO - Found target 1 with confidence 0.32
2025-07-22 19:08:59,961 - INFO - Found target 1 with confidence 0.31
2025-07-22 19:08:59,962 - INFO - Found 2 valid instances of target class 1
2025-07-22 19:08:59,963 - INFO - Cell 9 selected with IoU: 0.16
2025-07-22 19:08:59,963 - INFO - Final answers (IoU-based): [9]
2025-07-22 19:08:59,964 - INFO - Clicking 1 cells for dynamic captcha: [9]
2025-07-22 19:09:00,723 - INFO - Successfully clicked on cell 9
2025-07-22 19:09:00,732 - INFO - Capturing request: https://www.google.com/recaptcha/api2/replaceimage?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:09:00,782 - INFO - Capturing response: https://www.google.com/recaptcha/api2/replaceimage?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:09:00,911 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA40AuwpH9BZG3Sk7AfMqvmBKxg5b2u6tOIKvciOhKwmZZfhWyJD0mabWsnv2mwcilmVpyuyreXF99fB_wShqLP5liqEjAYn40riCpezcB7tuTDAQI8yWSvsHZkDuBXkxV-5d9SkiD6z7awgfht5uI3M_Up9ow6_CVigGmNgRamAdroD31q1If7L0I7g6R6ljigCyJupGstyYQVR8vIfeoSA-bsfjQ&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&id=2205c9182409ba18
2025-07-22 19:09:00,913 - INFO - Dynamic captcha iteration 1/5
2025-07-22 19:09:01,063 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA40AuwpH9BZG3Sk7AfMqvmBKxg5b2u6tOIKvciOhKwmZZfhWyJD0mabWsnv2mwcilmVpyuyreXF99fB_wShqLP5liqEjAYn40riCpezcB7tuTDAQI8yWSvsHZkDuBXkxV-5d9SkiD6z7awgfht5uI3M_Up9ow6_CVigGmNgRamAdroD31q1If7L0I7g6R6ljigCyJupGstyYQVR8vIfeoSA-bsfjQ&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&id=2205c9182409ba18 200 
2025-07-22 19:09:05,681 - INFO - Downloaded image 9.png successfully (attempt 1)
2025-07-22 19:09:05,694 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:09:05,695 - INFO - Running IoU-based solver for target class: 1
2025-07-22 19:09:05,710 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:09:05,711 - INFO - Image dimensions: 300x300
2025-07-22 19:09:05,932 - INFO - Found 2 objects in the image
2025-07-22 19:09:05,933 - INFO - Looking for target class: 1
2025-07-22 19:09:05,933 - INFO - Found 0 valid instances of target class 1
2025-07-22 19:09:05,934 - INFO - Final answers (IoU-based): []
2025-07-22 19:09:05,934 - INFO - No more answers found, dynamic captcha complete
2025-07-22 19:09:08,456 - INFO - Capturing request: https://www.google.com/recaptcha/api2/userverify?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:09:08,610 - INFO - Capturing response: https://www.google.com/recaptcha/api2/userverify?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:09:08,775 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA6KmAfnpYvRWFytSRv5i__wPa7NmoiznFOn8oCxj7WIsq2EEqXJ66CGw38ngZ0rZABippYjZEDFOvNlA28xNOxDxoFfUVHbMpqhe0ssr5P5BJInvLKT02Yb0QYNnYE9kPbD77Wf4tj9j-JhP9JFBlj6ORSq-YYeXSGzOQwDzQg2Ywth7_MJzMCTHtb2tJsSAdeguYMIdxfDCtObMyVI8phzSu6_Yg&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:09:08,832 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA6KmAfnpYvRWFytSRv5i__wPa7NmoiznFOn8oCxj7WIsq2EEqXJ66CGw38ngZ0rZABippYjZEDFOvNlA28xNOxDxoFfUVHbMpqhe0ssr5P5BJInvLKT02Yb0QYNnYE9kPbD77Wf4tj9j-JhP9JFBlj6ORSq-YYeXSGzOQwDzQg2Ywth7_MJzMCTHtb2tJsSAdeguYMIdxfDCtObMyVI8phzSu6_Yg&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:09:19,025 - WARNING - Verification failed: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff6a0f4e925+77845]
	GetHandleVerifier [0x0x7ff6a0f4e980+77936]
	(No symbol) [0x0x7ff6a0d09cda]
	(No symbol) [0x0x7ff6a0d606aa]
	(No symbol) [0x0x7ff6a0d6095c]
	(No symbol) [0x0x7ff6a0db3d07]
	(No symbol) [0x0x7ff6a0d8890f]
	(No symbol) [0x0x7ff6a0db0b07]
	(No symbol) [0x0x7ff6a0d886a3]
	(No symbol) [0x0x7ff6a0d51791]
	(No symbol) [0x0x7ff6a0d52523]
	GetHandleVerifier [0x0x7ff6a122683d+3059501]
	GetHandleVerifier [0x0x7ff6a1220bfd+3035885]
	GetHandleVerifier [0x0x7ff6a12403f0+3164896]
	GetHandleVerifier [0x0x7ff6a0f68c2e+185118]
	GetHandleVerifier [0x0x7ff6a0f7053f+216111]
	GetHandleVerifier [0x0x7ff6a0f572d4+113092]
	GetHandleVerifier [0x0x7ff6a0f57489+113529]
	GetHandleVerifier [0x0x7ff6a0f3e288+10616]
	BaseThreadInitThunk [0x0x7ffedafe1fd7+23]
	RtlUserThreadStart [0x0x7ffedccdd7d0+32]

2025-07-22 19:09:19,084 - INFO - Challenge attempt 6/10
2025-07-22 19:09:19,376 - INFO - Cleaned up 2 image files
2025-07-22 19:09:19,649 - INFO - Downloaded image 0.png successfully (attempt 1)
2025-07-22 19:09:19,650 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:09:19,651 - INFO - Running IoU-based solver for target class: 12
2025-07-22 19:09:19,656 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:09:19,656 - INFO - Image dimensions: 300x300
2025-07-22 19:09:19,835 - INFO - Found 0 objects in the image
2025-07-22 19:09:19,836 - INFO - Looking for target class: 12
2025-07-22 19:09:19,836 - INFO - Found 0 valid instances of target class 12
2025-07-22 19:09:19,836 - INFO - Final answers (IoU-based): []
2025-07-22 19:09:19,836 - WARNING - No answers found, reloading challenge
2025-07-22 19:09:20,112 - INFO - Capturing request: https://www.google.com/recaptcha/api2/reload?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:09:20,253 - INFO - Capturing response: https://www.google.com/recaptcha/api2/reload?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:09:20,430 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA6J8uiEvhXbz0PyqLLfe4QkXr9bc7IPp8kVo_fxvYxgqLtr-WlNKcVn2DbNSqGinNw02Hd1p0PpLEYN5DuggZYst47IhvCiu3bPDJHBUXL9w7j83PnX3alPOhhK-BHU0Rv0sQ8sPsdex9Vyszb8mMEl8qdYDi5SRqKKt3MjRXthd1eTEnCo_2HwNrezpJFxwTFiSe1eVb0C8Mv2tIrI-G1-095vuA&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:09:20,472 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA6J8uiEvhXbz0PyqLLfe4QkXr9bc7IPp8kVo_fxvYxgqLtr-WlNKcVn2DbNSqGinNw02Hd1p0PpLEYN5DuggZYst47IhvCiu3bPDJHBUXL9w7j83PnX3alPOhhK-BHU0Rv0sQ8sPsdex9Vyszb8mMEl8qdYDi5SRqKKt3MjRXthd1eTEnCo_2HwNrezpJFxwTFiSe1eVb0C8Mv2tIrI-G1-095vuA&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:09:20,803 - INFO - Challenge attempt 7/10
2025-07-22 19:09:20,922 - INFO - Cleaned up 1 image files
2025-07-22 19:09:21,290 - INFO - Downloaded image 0.png successfully (attempt 1)
2025-07-22 19:09:21,292 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:09:21,292 - INFO - Running IoU-based solver for target class: 10
2025-07-22 19:09:21,297 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:09:21,298 - INFO - Image dimensions: 300x300
2025-07-22 19:09:21,476 - INFO - Found 3 objects in the image
2025-07-22 19:09:21,477 - INFO - Looking for target class: 10
2025-07-22 19:09:21,478 - INFO - Found target 10 with confidence 0.79
2025-07-22 19:09:21,478 - INFO - Found 1 valid instances of target class 10
2025-07-22 19:09:21,479 - INFO - Cell 4 selected with IoU: 0.41
2025-07-22 19:09:21,479 - INFO - Final answers (IoU-based): [4]
2025-07-22 19:09:21,480 - INFO - Clicking 1 cells for dynamic captcha: [4]
2025-07-22 19:09:22,316 - INFO - Successfully clicked on cell 4
2025-07-22 19:09:22,331 - INFO - Capturing request: https://www.google.com/recaptcha/api2/replaceimage?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-
2025-07-22 19:09:22,487 - INFO - Dynamic captcha iteration 1/5
2025-07-22 19:09:22,491 - INFO - Capturing response: https://www.google.com/recaptcha/api2/replaceimage?k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ- 200 
2025-07-22 19:09:22,630 - INFO - Capturing request: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA7Okv4Fg-5MFjvT2OfOPHAjzWc26i3Ek7a6UPIMgyq_rVjR22L1nglh3UL-Drqx9mDPv3sVbIW0n7i3ltM1KZlDSypYJ9Ftc2rYoWxUooIcS3feIDs2jVn8AcSBcqzVkJoQvg9xjnNlvyi5YEZTcGSB1G3Um3kruxLxJe7h9Itc4Us2AzSt9DnoI-K64DZp5dE_DlUTiTeRwYKHSA4ss8NnRMmjuw&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&id=73257eaafefaaa72
2025-07-22 19:09:22,668 - INFO - Capturing response: https://www.google.com/recaptcha/api2/payload?p=06AFcWeA7Okv4Fg-5MFjvT2OfOPHAjzWc26i3Ek7a6UPIMgyq_rVjR22L1nglh3UL-Drqx9mDPv3sVbIW0n7i3ltM1KZlDSypYJ9Ftc2rYoWxUooIcS3feIDs2jVn8AcSBcqzVkJoQvg9xjnNlvyi5YEZTcGSB1G3Um3kruxLxJe7h9Itc4Us2AzSt9DnoI-K64DZp5dE_DlUTiTeRwYKHSA4ss8NnRMmjuw&k=6Le-wvkSAAAAAPBMRTvw0Q4Muexq9bi0DJwx_mJ-&id=73257eaafefaaa72 200 
2025-07-22 19:09:27,208 - INFO - Downloaded image 4.png successfully (attempt 1)
2025-07-22 19:09:27,222 - INFO - Found a 3x3 dynamic captcha
2025-07-22 19:09:27,222 - INFO - Running IoU-based solver for target class: 10
2025-07-22 19:09:27,235 - INFO - Image preprocessed with contrast enhancement.
2025-07-22 19:09:27,236 - INFO - Image dimensions: 300x300
2025-07-22 19:09:27,450 - INFO - Found 1 objects in the image
2025-07-22 19:09:27,450 - INFO - Looking for target class: 10
2025-07-22 19:09:27,450 - INFO - Found 0 valid instances of target class 10
2025-07-22 19:09:27,451 - INFO - Final answers (IoU-based): []
2025-07-22 19:09:27,451 - INFO - No more answers found, dynamic captcha complete
