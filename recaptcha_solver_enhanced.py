"""
reCAPTCHA solver implementation.
This module provides a complete solution for solving reCAPTCHA challenges.
"""
# Standard imports
import os
import re
import glob
import urllib3
import logging
from time import sleep, time
from pathlib import Path
from typing import Optional, List, Dict, Tuple

# Third-party imports
import cv2
import numpy as np
import requests
from PIL import Image
from ultralytics import YOLO
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from seleniumwire import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.edge.service import Service as EdgeService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('recaptcha_solver.log')
    ]
)

# Initialize the YOLO model globally to avoid reloading
model = None

def initialize_model(model_path: str = "./models/models.onnx") -> Optional[YOLO]:
    """Initialize YOLO model with error handling."""
    global model
    if model is None:
        try:
            if not Path(model_path).exists():
                logging.error(f"Model file not found: {model_path}")
                return None
            model = YOLO(model_path, task="detect")
            logging.info(f"YOLO model loaded successfully from {model_path}")
        except Exception as e:
            logging.error(f"Failed to load YOLO model: {e}")
            return None
    return model

def download_img(img_num: int, img_url: str, max_retries: int = 3) -> bool:
    """
    Download an image from a URL and save it with the specified number.
    :param img_num: Number to use in the filename.
    :param img_url: URL of the image to download.
    :param max_retries: Maximum number of retry attempts.
    :return: True if successful, False otherwise.
    """
    if not img_url or not img_url.strip():
        logging.error(f"Invalid URL provided for image {img_num}")
        return False

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }

    filename = f"{img_num}.png"

    for attempt in range(max_retries):
        try:
            response = requests.get(
                img_url,
                headers=headers,
                timeout=15,
                verify=False,
                stream=True
            )
            response.raise_for_status()

            # Validate content type
            content_type = response.headers.get('content-type', '').lower()
            if not any(img_type in content_type for img_type in ['image/', 'application/octet-stream']):
                logging.warning(f"Unexpected content type for image {img_num}: {content_type}")

            # Write image data
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # Verify file was created and has content
            if Path(filename).exists() and Path(filename).stat().st_size > 0:
                logging.info(f"Downloaded image {filename} successfully (attempt {attempt + 1})")
                return True
            else:
                logging.warning(f"Downloaded file {filename} is empty or doesn't exist")

        except requests.exceptions.RequestException as e:
            logging.warning(f"Network error downloading image {img_num} (attempt {attempt + 1}): {e}")
        except Exception as e:
            logging.error(f"Unexpected error downloading image {img_num} (attempt {attempt + 1}): {e}")

        if attempt < max_retries - 1:
            sleep(0.5 * (attempt + 1))  # Progressive delay

    logging.error(f"Failed to download image {img_num} after {max_retries} attempts")
    return False

def cleanup_images(pattern: str = "*.png") -> int:
    """
    Clean up image files matching the specified pattern.
    :param pattern: File pattern to match (default: "*.png").
    :return: Number of files deleted.
    """
    try:
        files_to_delete = glob.glob(pattern)
        count = 0

        for file_path in files_to_delete:
            try:
                file_obj = Path(file_path)
                if file_obj.exists() and file_obj.is_file():
                    file_obj.unlink()
                    count += 1
                    logging.debug(f"Deleted file: {file_path}")
            except Exception as e:
                logging.warning(f"Failed to delete {file_path}: {e}")

        if count > 0:
            logging.info(f"Cleaned up {count} image files")

        return count

    except Exception as e:
        logging.error(f"Error during cleanup: {e}")
        return 0

def find_between(s: str, first: str, last: str) -> str:
    """
    Find a substring between two substrings.
    :param s: string to search.
    :param first: first substring.
    :param last: last substring.
    :return: substring between first and last, or empty string if not found.
    """
    try:
        start = s.index(first) + len(first)
        end = s.index(last, start)
        return s[start:end]
    except ValueError:
        return ""

def random_delay(mu: float = 0.5, sigma: float = 0.2) -> None:
    """
    Random delay to simulate human behavior.
    :param mu: mean of normal distribution.
    :param sigma: standard deviation of normal distribution.
    """
    delay = np.random.normal(mu, sigma)
    # Ensure delay is at least 0.2 seconds but not too long
    delay = max(0.2, min(delay, mu + 3*sigma))
    logging.debug(f"Waiting for {delay:.2f} seconds...")
    sleep(delay)

def validate_image_file(filepath: str) -> bool:
    """
    Validate that a file exists and is a valid image.
    :param filepath: Path to the image file.
    :return: True if valid image, False otherwise.
    """
    try:
        if not Path(filepath).exists():
            return False

        # Try to open with PIL to validate
        with Image.open(filepath) as img:
            img.verify()
        return True
    except Exception:
        return False

def go_to_recaptcha_iframe1(driver):
    """
    Go to the first recaptcha iframe. (CheckBox)
    """
    driver.switch_to.default_content()
    recaptcha_iframe1 = WebDriverWait(driver=driver, timeout=60).until(
        EC.presence_of_element_located((By.XPATH, '//iframe[@title="reCAPTCHA"]')))
    driver.switch_to.frame(recaptcha_iframe1)

def go_to_recaptcha_iframe2(driver):
    """
    Go to the second recaptcha iframe. (Images)
    """
    driver.switch_to.default_content()
    recaptcha_iframe2 = WebDriverWait(driver=driver, timeout=60).until(
        EC.presence_of_element_located((By.XPATH, '//iframe[contains(@title, "challenge")]')))
    driver.switch_to.frame(recaptcha_iframe2)

def get_target_num(driver):
    """
    Get the target number from the recaptcha title.
    Maps reCAPTCHA challenge text to object class numbers.
    Extended with COCO and ImageNet classes.
    """
    target_mappings = {
        # Original mappings
        "bicycle": 1,
        "bus": 5,
        "boat": 8,
        "car": 2,
        "hydrant": 10,
        "motorcycle": 3,
        "traffic": 9,

        # Additional COCO classes
        "person": 0,
        "truck": 7,
        "airplane": 4,
        "train": 6,
        "stop sign": 11,
        "bench": 13,
        "bird": 14,
        "cat": 15,
        "dog": 16,
        "horse": 17,
        "sheep": 18,
        "cow": 19,
        "elephant": 20,

        # Additional ImageNet classes
        "crosswalk": 12,
        "bridge": 21,
        "palm tree": 22,
        "mountain": 23,
        "stairs": 24,
        "chimney": 25,
        "tractor": 26
    }

    target = WebDriverWait(driver, 10).until(EC.presence_of_element_located(
        (By.XPATH, '//div[@id="rc-imageselect"]//strong')))

    for term, value in target_mappings.items():
        if re.search(term, target.text.lower()): return value

    # Return a special code if no match is found
    logging.warning(f"Unknown target: {target.text}")
    return 1000

def preprocess_image(image_path: str, enhance_contrast: bool = True) -> Optional[np.ndarray]:
    """
    Preprocess the image to improve detection accuracy.
    :param image_path: Path to the image file.
    :param enhance_contrast: Whether to enhance contrast for better detection.
    :return: Preprocessed image as a numpy array.
    """
    try:
        if not Path(image_path).exists():
            logging.warning(f"Image file not found: {image_path}")
            return None

        image = cv2.imread(image_path)
        if image is None:
            logging.warning(f"Failed to load image at {image_path}")
            return None

        # Check if image is too small
        if image.shape[0] < 50 or image.shape[1] < 50:
            logging.warning(f"Image too small for processing: {image.shape}")
            return image  # Return original if too small

        if enhance_contrast:
            # Enhance contrast using CLAHE (Contrast Limited Adaptive Histogram Equalization)
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l_channel, a, b = cv2.split(lab)

            # Apply CLAHE to L-channel
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
            l_channel = clahe.apply(l_channel)

            # Merge channels and convert back to BGR
            enhanced_image = cv2.merge((l_channel, a, b))
            enhanced_image = cv2.cvtColor(enhanced_image, cv2.COLOR_LAB2BGR)

            # Apply slight sharpening
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            enhanced_image = cv2.filter2D(enhanced_image, -1, kernel)

            logging.info("Image preprocessed with contrast enhancement.")
            return enhanced_image
        else:
            # Simple noise reduction
            denoised = cv2.bilateralFilter(image, 9, 75, 75)
            logging.info("Image preprocessed with noise reduction.")
            return denoised

    except Exception as e:
        logging.error(f"Error during image preprocessing: {e}")
        return None

def calculate_iou(boxA: Tuple[float, float, float, float], boxB: Tuple[float, float, float, float]) -> float:
    """
    Calculate the Intersection over Union (IoU) of two bounding boxes.
    :param boxA: (x1, y1, x2, y2) for the first bounding box.
    :param boxB: (x1, y1, x2, y2) for the second bounding box.
    :return: The IoU value as a float.
    """
    # Ensure coordinates are in correct order
    x1A, y1A, x2A, y2A = boxA
    x1B, y1B, x2B, y2B = boxB

    # Swap coordinates if needed
    if x1A > x2A: x1A, x2A = x2A, x1A
    if y1A > y2A: y1A, y2A = y2A, y1A
    if x1B > x2B: x1B, x2B = x2B, x1B
    if y1B > y2B: y1B, y2B = y2B, y1B

    # Determine the coordinates of the intersection rectangle
    xA = max(x1A, x1B)
    yA = max(y1A, y1B)
    xB = min(x2A, x2B)
    yB = min(y2A, y2B)

    # Compute the area of intersection
    interArea = max(0, xB - xA) * max(0, yB - yA)

    # Compute the area of both bounding boxes
    boxAArea = (x2A - x1A) * (y2A - y1A)
    boxBArea = (x2B - x1B) * (y2B - y1B)

    # Avoid division by zero
    union_area = boxAArea + boxBArea - interArea
    if union_area <= 0:
        return 0.0

    # Compute the IoU
    iou = interArea / float(union_area)
    return max(0.0, min(1.0, iou))  # Clamp between 0 and 1

def dynamic_and_selection_solver(target_num: int, model) -> List[int]:
    """
    Get the answers from the recaptcha images using IoU-based detection and filtering.
    :param target_num: target number - class ID to detect.
    :param model: YOLO model for detection.
    :return: List of grid cell numbers containing the target object.
    """
    logging.info(f"Running IoU-based solver for target class: {target_num}")

    if model is None:
        logging.error("YOLO model is not initialized")
        return []

    # Preprocess image for better detection
    image = preprocess_image("0.png", enhance_contrast=True)
    if image is None:
        # Fallback to original image if preprocessing fails
        try:
            image = Image.open("0.png")
            image = np.asarray(image)
            if len(image.shape) == 3 and image.shape[2] == 4:  # RGBA
                image = cv2.cvtColor(image, cv2.COLOR_RGBA2BGR)
            elif len(image.shape) == 3 and image.shape[2] == 3:  # RGB
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
        except Exception as e:
            logging.error(f"Error loading fallback image: {e}")
            return []

    # Get image dimensions
    img_height, img_width = image.shape[:2]
    logging.info(f"Image dimensions: {img_width}x{img_height}")

    # Validate image size
    if img_height < 100 or img_width < 100:
        logging.warning(f"Image too small for reliable detection: {img_width}x{img_height}")
        return []

    try:
        # Run prediction with the model
        result = model.predict(image, task="detect", verbose=False, conf=0.2)

        if len(result) == 0 or not hasattr(result[0], 'boxes') or result[0].boxes is None:
            logging.warning("No detection results found")
            return []

        # Get detection boxes and confidence scores
        boxes = result[0].boxes.data

        logging.info(f"Found {len(result[0].boxes.cls)} objects in the image")
        logging.info(f"Looking for target class: {target_num}")

        # Filter detections by target class and confidence threshold
        target_boxes = []
        min_confidence = 0.25  # Confidence threshold

        for box in boxes:
            cls_id = int(box[5])
            conf = float(box[4])
            if cls_id == target_num:
                if conf >= min_confidence:
                    target_boxes.append(box)
                    logging.info(f"Found target {target_num} with confidence {conf:.2f}")
                else:
                    logging.debug(f"Skipping low confidence detection ({conf:.2f})")

        logging.info(f"Found {len(target_boxes)} valid instances of target class {target_num}")

        # Determine which grid cells are selected based on IoU
        answers = set()
        grid_rows, grid_cols = 3, 3
        cell_height = img_height / grid_rows
        cell_width = img_width / grid_cols
        iou_threshold = 0.1  # If 10% of a cell is covered by the object, select it

        for r in range(grid_rows):
            for c in range(grid_cols):
                # Define the bounding box for the current cell
                cell_x1 = c * cell_width
                cell_y1 = r * cell_height
                cell_x2 = (c + 1) * cell_width
                cell_y2 = (r + 1) * cell_height
                cell_box = (cell_x1, cell_y1, cell_x2, cell_y2)

                # Check for overlap with any of the target boxes
                for target_box in target_boxes:
                    obj_box = (float(target_box[0]), float(target_box[1]),
                              float(target_box[2]), float(target_box[3]))

                    iou = calculate_iou(obj_box, cell_box)

                    if iou > iou_threshold:
                        answer = r * grid_cols + c + 1
                        answers.add(answer)
                        logging.info(f"Cell {answer} selected with IoU: {iou:.2f}")
                        # Once a cell is selected, no need to check other boxes for it
                        break

        # Sort answers for consistent clicking order
        final_answers = sorted(list(answers))

        logging.info(f"Final answers (IoU-based): {final_answers}")
        return final_answers

    except Exception as e:
        logging.error(f"Error in dynamic_and_selection_solver: {e}")
        return []

def square_solver(target_num, model, title_text=""):
    """
    Get the answers from the square recaptcha images using IoU-based detection.
    :param target_num: target number.
    :param model: YOLO model for detection.
    :param title_text: The text from the reCAPTCHA challenge title.
    """
    logging.info(f"Running IoU-based square solver for target class: {target_num}")

    # Preprocess image for better detection
    image = preprocess_image("0.png")
    if image is None:
        # Fallback to original image if preprocessing fails
        try:
            image = Image.open("0.png")
            image = np.asarray(image)
        except Exception as e:
            logging.error(f"Error loading fallback image: {e}")
            return []

    # Get image dimensions
    img_height, img_width = image.shape[:2]
    logging.info(f"Image dimensions: {img_width}x{img_height}")

    # Run prediction with the model
    result = model.predict(image, task="detect", verbose=False)

    if len(result) == 0 or not hasattr(result[0], 'boxes'):
        logging.warning("No detection results found in square_solver")
        return []

    boxes = result[0].boxes.data

    logging.info(f"Found {len(result[0].boxes.cls)} objects in the image")
    logging.info(f"Looking for target class: {target_num}")

    # Filter detections by target class and confidence threshold
    target_boxes = []
    min_confidence = 0.25  # Lowered confidence for preprocessed images

    for box in boxes:
        cls_id = int(box[5])
        conf = float(box[4])
        if cls_id == target_num:
            if conf >= min_confidence:
                target_boxes.append(box)
                logging.info(f"Found target {target_num} with confidence {conf:.2f}")
            else:
                logging.info(f"Skipping low confidence detection ({conf:.2f})")

    logging.info(f"Found {len(target_boxes)} valid instances of target class {target_num}")

    # Determine which grid cells are selected based on IoU
    answers = set()
    # reCAPTCHA squares can be 3x3 or 4x4
    grid_dim = 4 if "4x4" in title_text or "16" in title_text else 3

    grid_rows, grid_cols = grid_dim, grid_dim
    cell_height = img_height / grid_rows
    cell_width = img_width / grid_cols
    iou_threshold = 0.1 # If 10% of a cell is covered by the object, select it

    for r in range(grid_rows):
        for c in range(grid_cols):
            # Define the bounding box for the current cell
            cell_x1 = c * cell_width
            cell_y1 = r * cell_height
            cell_x2 = (c + 1) * cell_width
            cell_y2 = (r + 1) * cell_height
            cell_box = (cell_x1, cell_y1, cell_x2, cell_y2)
            
            # Check for overlap with any of the target boxes
            for target_box in target_boxes:
                obj_box = (int(target_box[0]), int(target_box[1]), int(target_box[2]), int(target_box[3]))
                
                iou = calculate_iou(obj_box, cell_box)
                
                if iou > iou_threshold:
                    answer = r * grid_cols + c + 1
                    answers.add(answer)
                    logging.info(f"Cell {answer} selected with IoU: {iou:.2f} for a {grid_rows}x{grid_cols} grid")
                    # Once a cell is selected, no need to check other boxes for it
                    break 

    # Sort answers for consistent clicking order
    final_answers = sorted(list(answers))

    logging.info(f"Final answers (IoU-based, square): {final_answers}")

    return final_answers

def get_all_captcha_img_urls(driver):
    """
    Get all the image urls from the recaptcha.
    """
    images = WebDriverWait(driver, 10).until(EC.presence_of_all_elements_located(
        (By.XPATH, '//div[@id="rc-imageselect-target"]//img')))

    img_urls = []
    for img in images: img_urls.append(img.get_attribute("src"))

    return img_urls

def get_all_new_dynamic_captcha_img_urls(answers, before_img_urls, driver):
    """
    Get all the new image urls from the recaptcha.
    :param answers: answers from the recaptcha.
    :param before_img_urls: image urls before.
    """
    images = WebDriverWait(driver, 10).until(EC.presence_of_all_elements_located(
        (By.XPATH, '//div[@id="rc-imageselect-target"]//img')))
    img_urls = []

    # Get all the image urls
    for img in images:
        try: img_urls.append(img.get_attribute("src"))
        except:
            is_new = False
            return is_new, img_urls

    # Check if the image urls are the same as before
    index_common = []
    for answer in answers:
        if img_urls[answer-1] == before_img_urls[answer-1]: index_common.append(answer)

    # Return if the image urls are the same as before
    if len(index_common) >= 1:
        is_new = False
        return is_new, img_urls
    else:
        is_new = True
        return is_new, img_urls

def paste_new_img_on_main_img(main, new, loc):
    """
    Paste the new image on the main image with improved error handling and image processing.
    :param main: main image (PIL Image object).
    :param new: new image to paste (PIL Image object).
    :param loc: location (cell number) of the new image.
    """
    try:
        # Convert PIL images to numpy arrays if they aren't already
        if not isinstance(main, np.ndarray):
            main_array = np.array(main)
        else:
            main_array = main

        if not isinstance(new, np.ndarray):
            new_array = np.array(new)
        else:
            new_array = new

        # Create a copy of the main image to avoid modifying the original
        paste = np.copy(main_array)

        # Get main image dimensions
        main_height, main_width = paste.shape[:2]

        # Calculate grid cell size based on main image dimensions
        cell_height = main_height // 3
        cell_width = main_width // 3

        # Calculate row and column from location
        row = (loc - 1) // 3
        col = (loc - 1) % 3

        # Calculate start and end positions for pasting
        start_row = row * cell_height
        end_row = min((row + 1) * cell_height, main_height)
        start_col = col * cell_width
        end_col = min((col + 1) * cell_width, main_width)

        # Resize new image if needed to match the cell size
        cell_size = (end_col - start_col, end_row - start_row)
        if new_array.shape[0] != cell_size[1] or new_array.shape[1] != cell_size[0]:
            # Resize using OpenCV for better quality
            new_array = cv2.resize(new_array, cell_size, interpolation=cv2.INTER_AREA)

        # Ensure the arrays have compatible shapes before pasting
        if new_array.shape[0] == (end_row - start_row) and new_array.shape[1] == (end_col - start_col):
            # Paste the new image into the main image
            paste[start_row:end_row, start_col:end_col] = new_array

            # Convert to BGR for OpenCV
            if paste.shape[2] == 3:  # If it has 3 channels (RGB)
                paste_bgr = cv2.cvtColor(paste, cv2.COLOR_RGB2BGR)
            else:
                paste_bgr = paste

            # Save the result
            cv2.imwrite('0.png', paste_bgr)
            return True
        else:
            logging.error(f"Error: Image size mismatch. Cell: {end_row-start_row}x{end_col-start_col}, New image: {new_array.shape[0]}x{new_array.shape[1]}")
            return False

    except Exception as e:
        logging.error(f"Error in paste_new_img_on_main_img: {str(e)}")
        # Try a simpler approach as fallback
        try:
            paste = np.copy(main_array)
            row = (loc - 1) // 3
            col = (loc - 1) % 3

            # Use fixed cell size as fallback
            start_row, end_row = row * 100, (row + 1) * 100
            start_col, end_col = col * 100, (col + 1) * 100

            # Ensure we don't go out of bounds
            end_row = min(end_row, paste.shape[0])
            end_col = min(end_col, paste.shape[1])

            # Resize new image to fit the cell
            new_resized = cv2.resize(new_array, (end_col - start_col, end_row - start_row))

            # Paste and save
            paste[start_row:end_row, start_col:end_col] = new_resized
            paste_bgr = cv2.cvtColor(paste, cv2.COLOR_RGB2BGR)
            cv2.imwrite('0.png', paste_bgr)
            return True
        except Exception as fallback_error:
            logging.error(f"Fallback error in paste_new_img_on_main_img: {str(fallback_error)}")
            return False

def handle_challenge(driver, model):
    """
    Handles the main reCAPTCHA challenge loop.
    :param driver: Selenium driver.
    :param model: YOLO model for detection.
    :return: True if solved, False otherwise.
    """
    max_attempts = 10  # Prevent infinite loops
    attempt = 0

    while attempt < max_attempts:
        attempt += 1
        logging.info(f"Challenge attempt {attempt}/{max_attempts}")

        try:
            title_wrapper = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, 'rc-imageselect')))
            target_num = get_target_num(driver)

            if target_num == 1000:
                logging.warning("Skipping unknown target, reloading challenge")
                reload_challenge(driver)
                continue

            captcha_type, answers = process_image_and_get_answers(
                driver, model, target_num, title_wrapper.text
            )

            if answers:
                click_answers(driver, answers, captcha_type)

                if captcha_type == "dynamic":
                    if not handle_dynamic_captcha(driver, model, target_num, title_wrapper.text, answers):
                        logging.warning("Dynamic captcha handling failed, reloading")
                        reload_challenge(driver)
                        continue

                if verify_and_complete(driver):
                    logging.info(f"Challenge solved successfully on attempt {attempt}")
                    return True
            else:
                logging.warning("No answers found, reloading challenge")
                reload_challenge(driver)

        except Exception as e:
            logging.error(f"Error in handle_challenge attempt {attempt}: {e}")
            try:
                reload_challenge(driver)
            except Exception as reload_error:
                logging.error(f"Failed to reload challenge: {reload_error}")
                break

    logging.error(f"Failed to solve challenge after {max_attempts} attempts")
    return False

def handle_dynamic_captcha(driver, model, target_num, title_text, answers):
    """
    Handle dynamic captcha challenges with improved error handling.
    :param driver: Selenium driver.
    :param model: YOLO model.
    :param target_num: Target class ID.
    :param title_text: Challenge title text.
    :param answers: Initial answers.
    :return: True if successful, False otherwise.
    """
    try:
        img_urls = get_all_captcha_img_urls(driver)
        max_dynamic_iterations = 5  # Prevent infinite loops
        iteration = 0

        while iteration < max_dynamic_iterations:
            iteration += 1
            logging.info(f"Dynamic captcha iteration {iteration}/{max_dynamic_iterations}")

            before_img_urls = img_urls

            # Wait for new images
            timeout = 10
            while timeout > 0:
                is_new, img_urls = get_all_new_dynamic_captcha_img_urls(
                    answers, before_img_urls, driver)
                if is_new:
                    break
                sleep(0.5)
                timeout -= 0.5

            if timeout <= 0:
                logging.warning("Timeout waiting for new images")
                break

            # Download new images
            download_success = True
            for answer in answers:
                if not download_img(answer, img_urls[answer-1]):
                    download_success = False
                    break

            if not download_success:
                logging.error("Failed to download new images")
                return False

            # Paste new images
            for answer in answers:
                try:
                    main_img = Image.open("0.png")
                    new_img = Image.open(f"{answer}.png")
                    if not paste_new_img_on_main_img(main_img, new_img, answer):
                        logging.error(f"Failed to paste image for cell {answer}")
                        return False
                except Exception as e:
                    logging.error(f"Error processing image for cell {answer}: {e}")
                    return False

            _, answers = process_image_and_get_answers(
                driver, model, target_num, title_text, skip_download=True
            )

            if not answers:
                logging.info("No more answers found, dynamic captcha complete")
                break

            click_answers(driver, answers, "dynamic")

        return True

    except Exception as e:
        logging.error(f"Error in handle_dynamic_captcha: {e}")
        return False

def process_image_and_get_answers(driver, model, target_num, title_text, skip_download=False):
    """
    Downloads the image, runs detection, and returns answers.
    :param driver: Selenium driver.
    :param model: YOLO model.
    :param target_num: Target class ID.
    :param title_text: Text of the challenge title.
    :param skip_download: Skip downloading the image if it's already present.
    :return: Tuple of (captcha_type, answers).
    """
    if not skip_download:
        cleanup_images(pattern="*.png")
        img_urls = get_all_captcha_img_urls(driver)
        if not img_urls or not download_img(0, img_urls[0]):
            return None, []

    if "squares" in title_text:
        logging.info("Square captcha found....")
        answers = square_solver(target_num, model, title_text)
        return "squares", answers
    elif "none" in title_text:
        logging.info("Found a 3x3 dynamic captcha")
        answers = dynamic_and_selection_solver(target_num, model)
        return "dynamic", answers
    else:
        logging.info("Found a 3x3 one-time selection captcha")
        answers = dynamic_and_selection_solver(target_num, model)
        return "selection", answers

def click_answers(driver, answers, captcha_type):
    """
    Clicks on the answer cells.
    :param driver: Selenium driver.
    :param answers: List of cell numbers to click.
    :param captcha_type: Type of captcha (used for logging context).
    """
    if not answers:
        logging.warning("No answers provided to click")
        return

    logging.info(f"Clicking {len(answers)} cells for {captcha_type} captcha: {answers}")

    for answer in answers:
        try:
            element = WebDriverWait(driver, 10).until(EC.element_to_be_clickable(
                (By.XPATH, f'(//div[@id="rc-imageselect-target"]//td)[{answer}]')))
            random_delay(mu=0.5, sigma=0.2)
            element.click()
            logging.info(f"Successfully clicked on cell {answer}")
        except Exception as e:
            logging.error(f"Error clicking cell {answer}: {e}")
            # Continue with other cells even if one fails

def reload_challenge(driver):
    """
    Clicks the reload button to get a new challenge.
    """
    try:
        reload = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.ID, 'recaptcha-reload-button')))
        reload.click()
        random_delay(mu=0.5, sigma=0.1)
    except Exception as e:
        logging.error(f"Error clicking reload button: {e}")

def verify_and_complete(driver):
    """
    Clicks the verify button and checks for success.
    :param driver: Selenium driver.
    :return: True if solved, False otherwise.
    """
    try:
        verify = WebDriverWait(driver, 15).until(EC.element_to_be_clickable(
            (By.ID, "recaptcha-verify-button")))
        random_delay(mu=2.5, sigma=0.5)
        verify.click()

        go_to_recaptcha_iframe1(driver)
        WebDriverWait(driver, 4).until(
            EC.presence_of_element_located((By.XPATH, '//span[contains(@aria-checked, "true")]')))
        logging.info("solved")
        driver.switch_to.default_content()
        return True
    except Exception as e:
        logging.warning(f"Verification failed: {e}")
        go_to_recaptcha_iframe2(driver)
        return False

def solve_recaptcha(driver):
    """
    Solve the recaptcha.
    :param driver: selenium driver.
    """
    try:
        # Initialize model if not already done
        current_model = initialize_model()
        if current_model is None:
            logging.error("Failed to initialize YOLO model")
            return False

        cleanup_images(pattern="*.png")
        go_to_recaptcha_iframe1(driver)
        WebDriverWait(driver, 10).until(EC.element_to_be_clickable(
            (By.XPATH, '//div[@class="recaptcha-checkbox-border"]'))).click()
        go_to_recaptcha_iframe2(driver)
        return handle_challenge(driver, current_model)
    except Exception as e:
        logging.error(f"Error in solve_recaptcha: {e}")
        return False
    finally:
        cleanup_count = cleanup_images(pattern="*.png")
        if cleanup_count > 0:
            logging.info(f"Cleaned up {cleanup_count} image files")

def find_chrome_binary():
    """
    Find Chrome binary automatically on Windows.
    :return: Path to Chrome binary or None if not found.
    """
    possible_paths = [
        r'C:\Program Files\Google\Chrome\Application\chrome.exe',
        r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
        r'C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe'.format(os.getenv('USERNAME', 'Admin')),
        r'C:\Users\<USER>\AppData\Local\Google\Chrome SxS\Application\chrome.exe'.format(os.getenv('USERNAME', 'Admin')),
    ]

    for path in possible_paths:
        if os.path.exists(path):
            logging.info(f"Found Chrome binary at: {path}")
            return path

    logging.warning("Chrome binary not found in common locations")
    return None

def get_driver(browser, seleniumwire_options, chrome_binary_path=None):
    browser = (browser or 'chrome').lower()
    if browser == 'chrome':
        options = webdriver.ChromeOptions()

        # Handle Chrome binary path
        if chrome_binary_path:
            if os.path.exists(chrome_binary_path):
                options.binary_location = chrome_binary_path
                logging.info(f"Using provided Chrome binary: {chrome_binary_path}")
            else:
                logging.warning(f"Provided Chrome binary path does not exist: {chrome_binary_path}")
                auto_path = find_chrome_binary()
                if auto_path:
                    options.binary_location = auto_path
        else:
            auto_path = find_chrome_binary()
            if auto_path:
                options.binary_location = auto_path

        # Chrome options for stability and compatibility
        options.add_argument('--no-sandbox')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--allow-insecure-localhost')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-web-security')
        options.add_argument('--accept-insecure-certs')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--blink-settings=imagesEnabled=true')
        options.add_argument('--lang=en-US')
        options.add_argument('--window-size=1280,1024')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

        # Always run in visible mode (headless mode removed)
        options.add_argument('--disable-notifications')

        try:
            service = ChromeService()
            driver = webdriver.Chrome(service=service, options=options, seleniumwire_options=seleniumwire_options)
        except Exception as e:
            logging.error(f"Failed to create Chrome driver: {e}")
            raise
    elif browser == 'firefox':
        options = webdriver.FirefoxOptions()
        options.set_preference('intl.accept_languages', 'en-US')
        options.set_preference('permissions.default.image', 1)
        options.set_preference('dom.webnotifications.enabled', False)
        # Always run in visible mode (headless mode removed)
        service = FirefoxService()
        driver = webdriver.Firefox(service=service, options=options, seleniumwire_options=seleniumwire_options)
    elif browser == 'edge':
        options = webdriver.EdgeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--disable-gpu')
        options.add_argument('--lang=en-US')
        options.add_argument('--window-size=1280,1024')
        # Always run in visible mode (headless mode removed)
        service = EdgeService()
        driver = webdriver.Edge(service=service, options=options, seleniumwire_options=seleniumwire_options)
    else:
        raise ValueError(f"Unsupported browser: {browser}")
    driver.scopes = ['.*google.com/recaptcha.*']
    return driver

def solver(url: str, cookies: Optional[List[Dict]] = None, proxy: Optional[str] = None,
          browser: Optional[str] = None, chrome_binary_path: Optional[str] = None) -> Dict:
    """
    Main solver function for reCAPTCHA challenges.

    :param url: URL containing the reCAPTCHA challenge
    :param cookies: Optional list of cookies to add to the session
    :param proxy: Optional proxy string in format 'host:port'
    :param browser: Browser to use ('chrome', 'firefox', 'edge')
    :param chrome_binary_path: Optional path to Chrome binary
    :return: Dictionary with results including token, cookies, time_taken, and success status
    """
    start_time = time()

    # Initialize model first
    if initialize_model() is None:
        return {
            "success": False,
            "error": "Failed to initialize YOLO model",
            "time_taken": 0
        }

    # Clean up any existing image files before starting
    cleanup_images(pattern="*.png")

    # Disable SSL warnings
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # Set environment variable to ignore certificate errors
    os.environ['PYTHONHTTPSVERIFY'] = '0'

    # Set Up seleniumwire options with proxy if provided
    seleniumwire_options = {
        'verify_ssl': False,
        'disable_encoding': True,
        'suppress_connection_errors': True,
        'request_storage_base_dir': None  # Use memory storage
    }

    if proxy:
        try:
            # Validate proxy format
            if ':' not in proxy:
                raise ValueError("Proxy must be in format 'host:port'")

            seleniumwire_options['proxy'] = {
                'http': f'http://{proxy}',
                'https': f'https://{proxy}',
                'no_proxy': 'localhost,127.0.0.1'
            }
            logging.info(f"Using proxy: {proxy}")
        except Exception as e:
            logging.warning(f"Invalid proxy configuration: {e}")

    driver = None
    try:
        driver = get_driver(browser, seleniumwire_options, chrome_binary_path)
        driver.set_page_load_timeout(45)
        driver.implicitly_wait(10)

        logging.info(f"Navigating to {url}")
        driver.get(url)

        # Add cookies if provided
        if cookies:
            for cookie in cookies:
                try:
                    # Validate cookie format
                    if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                        driver.add_cookie(cookie)
                        logging.debug(f"Added cookie: {cookie['name']}")
                    else:
                        logging.warning(f"Invalid cookie format: {cookie}")
                except Exception as e:
                    logging.warning(f"Failed to add cookie {cookie}: {e}")

        # Solve the reCAPTCHA
        solve_success = solve_recaptcha(driver)

        # Extract token from network requests
        token = None
        try:
            for request in getattr(driver, 'requests', []):
                if 'recaptcha/api2/userverify' in request.url and request.response:
                    try:
                        body = request.response.body.decode('utf-8', errors='ignore')
                        # The response is not always JSON, it's prefixed with )]}'
                        json_str_match = re.search(r'\["rresp","([^"]+)"', body)
                        if json_str_match:
                            token = json_str_match.group(1)
                            logging.info(f"Found reCAPTCHA token: {token[:20]}...")
                            break
                    except Exception as e:
                        logging.debug(f"Error parsing userverify response: {e}")
                        continue
        except Exception as e:
            logging.warning(f"Error extracting token from requests: {e}")

        # Get final cookies
        cookies_out = []
        try:
            cookies_out = driver.get_cookies()
        except Exception as e:
            logging.warning(f"Failed to get cookies: {e}")

        time_taken = round(time() - start_time, 2)
        success = token is not None and solve_success

        return {
            "recaptcha_token": token,
            "cookies": cookies_out,
            "time_taken": time_taken,
            "success": success
        }

    except Exception as e:
        logging.error(f"Error in solver: {str(e)}")
        time_taken = round(time() - start_time, 2)
        return {
            "success": False,
            "error": str(e),
            "time_taken": time_taken
        }
    finally:
        # Ensure driver is properly closed
        if driver:
            try:
                driver.quit()
                logging.info("Driver closed successfully")
            except Exception as close_error:
                logging.error(f"Error closing driver: {close_error}")

        # Final cleanup
        cleanup_count = cleanup_images(pattern="*.png")
        if cleanup_count > 0:
            logging.info(f"Final cleanup: removed {cleanup_count} image files")


def main():
    """
    Main function for testing the reCAPTCHA solver.
    """
    import argparse

    parser = argparse.ArgumentParser(description='reCAPTCHA Solver')
    parser.add_argument('url', help='URL containing reCAPTCHA challenge')
    parser.add_argument('--browser', choices=['chrome', 'firefox', 'edge'],
                       default='chrome', help='Browser to use')
    parser.add_argument('--proxy', help='Proxy in format host:port')
    parser.add_argument('--chrome-binary', help='Path to Chrome binary')
    parser.add_argument('--model-path', default='./models/models.onnx',
                       help='Path to YOLO model file')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize model with custom path
    global model
    model = initialize_model(args.model_path)
    if model is None:
        print("Failed to initialize YOLO model. Please check the model path.")
        return 1

    print(f"Solving reCAPTCHA at: {args.url}")
    result = solver(
        url=args.url,
        browser=args.browser,
        proxy=args.proxy,
        chrome_binary_path=args.chrome_binary
    )

    print(f"Result: {result}")
    return 0 if result['success'] else 1


if __name__ == "__main__":
    exit(main())
